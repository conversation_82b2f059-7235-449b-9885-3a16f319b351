"use client"

import React from "react"
import Link from "next/link"
import { usePathname, useSearchParams } from "next/navigation"
import {
  ChartColumn,
  ChevronRight,
  CircleDollarSign,
  Wallet,
  type LucideIcon,
} from "lucide-react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"

type Page = {
  title: string
  url: string
  icon: LucideIcon
  subPages?: Page[]
}

const PAGES: Page[] = [
  {
    title: "Summary",
    url: "/summary",
    icon: ChartColumn,
    subPages: [
      {
        title: "Profit & Loss",
        url: "/profit-and-loss",
        icon: Wallet,
      },
      {
        title: "Credit & Balance Sheet",
        url: "/credit-and-balance-sheet",
        icon: Wallet,
      },
      {
        title: "Operations & HR",
        url: "/operations-and-hr",
        icon: Wallet,
      },
      {
        title: "Patients",
        url: "/patients",
        icon: Wallet,
      },
      {
        title: "Insights",
        url: "/insights",
        icon: Wallet,
      },
    ],
  },
  {
    title: "Performance",
    url: "/performance",
    icon: CircleDollarSign,
  },
]

const NavMain = () => {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const queryString = searchParams.toString()
  const querySuffix = queryString ? `?${queryString}` : ""

  const { setOpenMobile } = useSidebar()

  return (
    <SidebarGroup>
      <SidebarMenu>
        {PAGES.map((page) => {
          if (!page.subPages) {
            const isActive = pathname === page.url

            return (
              <SidebarMenuItem key={page.title}>
                <SidebarMenuButton
                  tooltip={page.title}
                  className="data-[active=true]:text-primary data-[active=true]:bg-background data-[active=true]:shadow-sm"
                  isActive={isActive}
                  onClick={() => setOpenMobile(false)}
                  asChild
                >
                  <Link href={page.url}>
                    <page.icon />
                    <span>{page.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            )
          }

          const defaultOpen = page.subPages.some(
            (subPage) => pathname === page.url + subPage.url
          )

          return (
            <Collapsible
              key={page.title}
              title={page.title}
              className="group/collapsible"
              defaultOpen={defaultOpen}
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton tooltip={page.title}>
                    <page.icon />
                    <span>{page.title}</span>
                    <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <SidebarGroupContent>
                    <SidebarMenuSub className="group-data-[collapsible=icon]:mx-0 group-data-[collapsible=icon]:mt-1 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:gap-1 group-data-[collapsible=icon]:border-none group-data-[collapsible=icon]:p-0">
                      {page.subPages.map((subPage) => {
                        const isSubPageActive =
                          pathname === page.url + subPage.url

                        return (
                          <SidebarMenuSubItem key={subPage.title}>
                            <SidebarMenuSubButton
                              tooltip={subPage.title}
                              className="data-[active=true]:text-primary data-[active=true]:bg-background data-[active=true]:[&>svg]:text-primary group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:size-8! data-[active=true]:shadow-sm"
                              onClick={() => setOpenMobile(false)}
                              isActive={isSubPageActive}
                              asChild
                            >
                              <Link href={page.url + subPage.url + querySuffix}>
                                <subPage.icon className="hidden group-data-[collapsible=icon]:block" />
                                <span>{subPage.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        )
                      })}
                    </SidebarMenuSub>
                  </SidebarGroupContent>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}

export default NavMain
