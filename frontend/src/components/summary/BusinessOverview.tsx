"use client"

import React from "react"
import dynamic from "next/dynamic"
import { <PERSON>, Line, LineChart, <PERSON>, <PERSON>hart, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

import { ChartDataPoint } from "@/types/summary"
import { formatAbbreviatedCurrency, formatCurrency } from "@/lib/number"
import { cn } from "@/lib/utils"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useBusinessOverview } from "@/services/summary"

export const CustomPieChart = dynamic(() => Promise.resolve(_CustomPieChart), {
  ssr: false,
})

const BusinessLineChart = ({ data }: { data: ChartDataPoint[] }) => {
  const { filters } = useSummaryFilters()

  return (
    <div className="-mt-3.5 flex flex-col items-center">
      <LineChart data={data} width={40} height={30}>
        <Line
          dataKey="value"
          stroke="var(--color-green-600)"
          strokeWidth={2}
          dot={false}
        />
        <YAxis hide domain={["dataMin", "dataMax"]} />
      </LineChart>

      <p className="text-center text-xs">Last {filters.last_n_years} yrs</p>
    </div>
  )
}

const BusinessOverview = () => {
  const { filters } = useSummaryFilters()
  const { data, isLoading, error } = useBusinessOverview(filters)

  if (isLoading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <Skeleton key={index} className="h-30 rounded-lg" />
        ))}
      </div>
    )
  }

  if (error || !data) {
    const emptyData = [
      { name: "2022", value: 0 },
      { name: "2023", value: 0 },
      { name: "2024", value: 0 },
      { name: "2025", value: 0 },
    ]

    return (
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-6">
        <OverviewCard
          title="Gross Revenue"
          value="$0"
          amount={0}
          unit="%"
          topLeftItem={<CustomPieChart />}
          bottomRightItem={<BusinessLineChart data={emptyData} />}
        />
        <OverviewCard
          title="EBITDA"
          value="$0"
          amount={0}
          unit="%"
          topLeftItem={<CustomPieChart />}
          bottomRightItem={<BusinessLineChart data={emptyData} />}
        />
        <OverviewCard
          title="Bank Debt"
          value="$0"
          amount={0}
          unit="%"
          topLeftItem={
            <div className="mt-1 text-center">
              <p className="text-muted-foreground text-base font-bold text-nowrap">
                0.0pts
              </p>
              <p className="text-[10px] leading-none text-nowrap">Debt Ratio</p>
            </div>
          }
          bottomRightItem={<BusinessLineChart data={emptyData} />}
        />
        <OverviewCard
          title="Cash Balance"
          value="$0"
          amount={0}
          unit="%"
          topLeftItem={
            <div className="mt-1 text-center">
              <p className="text-muted-foreground text-base font-bold text-nowrap">
                0.0pts
              </p>
              <p className="text-[10px] leading-none text-nowrap">Cash Ratio</p>
            </div>
          }
          bottomRightItem={<BusinessLineChart data={emptyData} />}
        />
        <OverviewCard
          title="FTE"
          value="0"
          amount={0}
          unit="%"
          bottomRightItem={<BusinessLineChart data={emptyData} />}
        />
        <OverviewCard
          title="Net Profit"
          value="$0"
          amount={0}
          unit="%"
          bottomRightItem={<BusinessLineChart data={emptyData} />}
        />
      </div>
    )
  }

  return (
    <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-6">
      <OverviewCard
        title="Gross Revenue"
        value={`$${formatAbbreviatedCurrency(data.gross_revenue.value)}`}
        amount={data.gross_revenue.percentage}
        unit="%"
        topLeftItem={<CustomPieChart />}
        bottomRightItem={
          <BusinessLineChart data={data.gross_revenue.chart_data} />
        }
      />

      <OverviewCard
        title="EBITDA"
        value={`$${formatAbbreviatedCurrency(data.ebitda.value)}`}
        amount={data.ebitda.percentage}
        unit="%"
        topLeftItem={<CustomPieChart />}
        bottomRightItem={<BusinessLineChart data={data.ebitda.chart_data} />}
      />

      <OverviewCard
        title="Bank Debt"
        value={`$${formatAbbreviatedCurrency(data.bank_debt.value)}`}
        amount={data.bank_debt.percentage}
        unit="%"
        topLeftItem={
          data.bank_debt.additional_info ? (
            <div className="mt-1 text-center">
              <p
                className={cn(
                  "text-base font-bold text-nowrap",
                  data.bank_debt.value > 0
                    ? "text-red-600"
                    : "text-muted-foreground"
                )}
              >
                {data.bank_debt.additional_info.value}
              </p>
              <p className="text-[10px] leading-none text-nowrap">
                {data.bank_debt.additional_info.label}
              </p>
            </div>
          ) : (
            <CustomPieChart />
          )
        }
        bottomRightItem={<BusinessLineChart data={data.bank_debt.chart_data} />}
      />

      <OverviewCard
        title="Cash Balance"
        value={`$${formatAbbreviatedCurrency(data.cash_balance.value)}`}
        amount={data.cash_balance.percentage}
        unit="%"
        topLeftItem={
          data.cash_balance.additional_info ? (
            <div className="mt-1 text-center">
              <p
                className={cn(
                  "text-base font-bold text-nowrap",
                  data.cash_balance.value > 0
                    ? "text-green-600"
                    : "text-red-600"
                )}
              >
                {data.cash_balance.additional_info.value}
              </p>
              <p className="text-[10px] leading-none text-nowrap">
                {data.cash_balance.additional_info.label}
              </p>
            </div>
          ) : (
            <CustomPieChart />
          )
        }
        bottomRightItem={
          <BusinessLineChart data={data.cash_balance.chart_data} />
        }
      />

      <OverviewCard
        title="FTE"
        value={formatCurrency(data.fte.value, 0)}
        amount={data.fte.percentage}
        unit="%"
        bottomRightItem={<BusinessLineChart data={data.fte.chart_data} />}
      />

      <OverviewCard
        title="Net Profit"
        value={`$${formatAbbreviatedCurrency(data.net_profit.value)}`}
        amount={data.net_profit.percentage}
        unit="%"
        bottomRightItem={
          <BusinessLineChart data={data.net_profit.chart_data} />
        }
      />
    </div>
  )
}

export default BusinessOverview

export const OverviewCard = ({
  title,
  value,
  amount,
  unit,
  topLeftItem,
  bottomRightItem,
}: {
  title: string
  value: string
  amount: number
  unit: string
  topLeftItem?: React.ReactNode
  bottomRightItem?: React.ReactNode
}) => (
  <Card className="rounded-lg py-3 shadow-xs">
    <CardContent className="flex flex-1 flex-col gap-2 px-3">
      <div className="flex h-4 items-center justify-between gap-2">
        <CardTitle className="text-muted-foreground text-sm leading-tight font-medium">
          {title}
        </CardTitle>

        {topLeftItem}
      </div>

      <p className="-mt-0.5 text-2xl font-bold">{value}</p>

      <div className="mt-auto flex items-end justify-between gap-2">
        <div
          className={cn(
            "rounded-full px-3 py-1.5 text-sm font-semibold",
            amount === 0 && "bg-muted text-muted-foreground",
            amount < 0 && "bg-red-100 text-red-600",
            amount > 0 && "bg-green-100 text-green-600"
          )}
        >
          {amount > 0 ? "+" : ""}
          {amount}
          {unit}
        </div>

        {bottomRightItem}
      </div>
    </CardContent>
  </Card>
)

const _CustomPieChart = () => {
  const data = [
    { name: "2023", value: 100, color: "var(--color-green-400)" },
    { name: "2024", value: 100, color: "var(--color-green-600)" },
    { name: "2025", value: 100, color: "var(--color-green-800)" },
  ]

  return (
    <div className="relative mt-1 shrink-0">
      <PieChart width={48} height={32}>
        <Pie
          data={data}
          dataKey="value"
          cx="50%"
          cy="100%"
          startAngle={180}
          endAngle={0}
          innerRadius={18}
          outerRadius={24}
          paddingAngle={0}
        >
          {data.map((data, index) => (
            <Cell key={`cell-${index}`} fill={data.color} />
          ))}
        </Pie>
      </PieChart>

      <div className="absolute inset-0 flex items-end justify-center pb-px">
        <span className="text-xs font-bold">90%</span>
      </div>
    </div>
  )
}
