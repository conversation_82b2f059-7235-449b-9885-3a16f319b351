from rest_framework.viewsets import ModelViewSet
from datetime import datetime
from collections import defaultdict, OrderedDict
from django.db.models import Decimal<PERSON>ield, Sum, Value, Q
from django.db.models.functions import Coalesce, ExtractYear
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from rest_framework.decorators import action
from rest_framework import status
from fuzzywuzzy import fuzz, process
import re
import traceback
from accounting.models import ChartofAccount, JournalEntryTransaction, Clinic, Employee
from utils.response_template import custom_error_response, custom_success_response

# Cache timeout in seconds (24 hours)
CACHE_TTL = 60 * 60 * 24


class ProfitAndLossViewSet(ModelViewSet):
    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        try:
            return custom_success_response({})
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="revenue/chart")
    def revenue_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            chart_of_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values_list("id", flat=True)

            yearly_data = []
            revenue_by_year = {}

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                journal_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=chart_of_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                revenue_data = journal_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                revenue_by_year[y] = (
                    revenue_data["total_credit"] - revenue_data["total_debit"]
                )

            for y in range(start_year, end_year + 1):
                revenue = revenue_by_year[y]
                prev_revenue = revenue_by_year.get(y - 1)

                if prev_revenue and prev_revenue != 0:
                    percentage = float((revenue - prev_revenue) / prev_revenue * 100)
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(revenue, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="ebitda/chart")
    def ebitda_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Revenue accounts
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            expense_accounts = (
                ChartofAccount.objects.filter(
                    account_type="Expenses",
                    entity__currency=currency,
                )
                .exclude(
                    Q(account_name__icontains="interest")
                    | Q(account_name__icontains="tax")
                    | Q(account_name__icontains="depreciation")
                    | Q(account_name__icontains="amortization")
                )
                .values_list("id", flat=True)
            )

            ebitda_by_year = {}
            yearly_data = []

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                revenue_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=revenue_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                revenue_data = revenue_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_revenue = (
                    revenue_data["total_credit"] - revenue_data["total_debit"]
                )

                expense_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=expense_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                expense_data = expense_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_expense = (
                    expense_data["total_debit"] - expense_data["total_credit"]
                )

                ebitda_by_year[y] = total_revenue - total_expense

            for y in range(start_year, end_year + 1):
                ebitda = ebitda_by_year[y]
                prev_ebitda = ebitda_by_year.get(y - 1)

                if prev_ebitda and prev_ebitda != 0:
                    percentage = float((ebitda - prev_ebitda) / prev_ebitda * 100)
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(ebitda, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="net-profit/chart")
    def net_profit_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            expense_accounts = ChartofAccount.objects.filter(
                account_type__in=["Expenses"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            net_profit_by_year = {}
            yearly_data = []

            for y in range(start_year - 1, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, 12, 31)

                revenue_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=revenue_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                revenue_data = revenue_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_revenue = (
                    revenue_data["total_credit"] - revenue_data["total_debit"]
                )

                expense_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=expense_accounts,
                    transaction_date__range=[start_date, end_date],
                )

                expense_data = expense_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                total_expense = (
                    expense_data["total_debit"] - expense_data["total_credit"]
                )

                net_profit_by_year[y] = total_revenue - total_expense

            for y in range(start_year, end_year + 1):
                net_profit = net_profit_by_year[y]
                prev_net_profit = net_profit_by_year.get(y - 1)

                if prev_net_profit and prev_net_profit != 0:
                    percentage = float(
                        (net_profit - prev_net_profit) / prev_net_profit * 100
                    )
                else:
                    percentage = 0.0

                yearly_data.append(
                    {
                        "name": str(y),
                        "amount": float(round(net_profit, 2)),
                        "percentage": round(percentage, 1),
                    }
                )

            return custom_success_response(yearly_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(
        detail=False, methods=["get"], url_path="revenue-breakdown/by-segment/chart"
    )
    def revenue_breakdown_by_segment_chart(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            today = datetime.now()
            month_day = (today.month, today.day)

            chart_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"], entity__currency=currency
            ).values("id", "clinic_code")

            coa_id_to_clinic_code = {
                str(c["id"]): c["clinic_code"] for c in chart_accounts
            }
            coa_ids = list(coa_id_to_clinic_code.keys())

            clinic_code_to_segment = dict(Clinic.objects.values_list("code", "segment"))

            year_data = {}
            all_segments = set()

            for y in range(start_year, end_year + 1):
                start_date = datetime(y, 1, 1)
                end_date = datetime(y, month_day[0], month_day[1])

                journal_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=coa_ids,
                    transaction_date__range=[start_date, end_date],
                ).select_related("chart_of_account")

                revenue_by_segment = defaultdict(float)

                for entry in journal_entries:
                    coa_id = str(entry.chart_of_account_id)
                    clinic_code = coa_id_to_clinic_code.get(coa_id)
                    segment = clinic_code_to_segment.get(clinic_code, "Others")

                    credit = float(entry.reporting_credit_amount or 0)
                    debit = float(entry.reporting_debit_amount or 0)
                    amount = credit - debit
                    revenue_by_segment[segment] += amount

                all_segments.update(revenue_by_segment.keys())

                sorted_segments = sorted(
                    [s for s in revenue_by_segment if s != "Others"]
                )
                if "Others" in revenue_by_segment:
                    sorted_segments.append("Others")

                total = sum(revenue_by_segment.values())

                record = {
                    "name": f"YTD<br/>{y}",
                    "total": round(total, 2),
                }

                for seg in sorted_segments:
                    record[seg] = round(revenue_by_segment.get(seg, 0.0), 2)

                year_data[y] = record

            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            formatted_data = []
            for y in range(start_year, end_year + 1):
                record = year_data[y]
                for seg in final_segments:
                    if seg not in record:
                        record[seg] = 0.0

                ordered_record = OrderedDict(
                    [("name", record["name"]), ("total", record["total"])]
                    + [(seg, record[seg]) for seg in final_segments]
                )
                formatted_data.append(ordered_record)

            return custom_success_response(formatted_data)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


class OperationsAndHRViewSet(ModelViewSet):
    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="overview")
    def overview(self, request):
        try:
            currency = request.query_params.get("currency", "SGD")
            year = int(request.query_params.get("year", datetime.now().year))

            # Define resignation reason categories
            VOLUNTARY_REASONS = {
                "Back to School",
                "Better Benefits",
                "Career Progression",
                "End of Contract",
                "Family Reasons",
                "Fellow Colleagues",
                "Further Education",
                "Health Reasons",
                "Job Mismatch",
                "Remunerations",
                "Resigned",
                "Supervisor / Management",
            }

            INVOLUNTARY_REASONS = {
                "Company Direction",
                "Dismissed",
                "Not for Rehire",
                "Terminated",
            }

            # Calculate revenue per FTE
            revenue_accounts = ChartofAccount.objects.filter(
                account_type__in=["Revenues", "Income"],
                entity__currency=currency,
            ).values_list("id", flat=True)

            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)

            revenue_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=revenue_accounts,
                transaction_date__range=[start_date, end_date],
            )

            revenue_data = revenue_entries.aggregate(
                total_credit=Coalesce(
                    Sum("reporting_credit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
                total_debit=Coalesce(
                    Sum("reporting_debit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )

            total_revenue = revenue_data["total_credit"] - revenue_data["total_debit"]

            # Calculate FTE count
            FTE_WEIGHTS = {
                "Permanent Full Time": 1.0,
                "Permanent Part Time": 0.5,
                "Casual Workers": 0.3,
                "Intern": 0.2,
                "Visting Consultant": 0.4,
                "Contract Basis": 0.5,
            }

            employees = Employee.objects.filter(
                joined_date__year__lte=year, resignation_date__isnull=True
            )

            total_fte = 0
            total_employees = 0
            doctor_count = 0

            for emp in employees:
                emp_type = getattr(emp, "category", None)
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                total_fte += fte_value
                total_employees += 1

                if emp.occupation == "Doctor":
                    doctor_count += 1

            # Calculate metrics
            revenue_per_fte = float(total_revenue) / total_fte if total_fte > 0 else 0
            revenue_per_doctor = (
                float(total_revenue) / doctor_count if doctor_count > 0 else 0
            )

            # Calculate previous year data for percentage changes
            prev_year = year - 1
            prev_start_date = datetime(prev_year, 1, 1)
            prev_end_date = datetime(prev_year, 12, 31)

            prev_revenue_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__id__in=revenue_accounts,
                transaction_date__range=[prev_start_date, prev_end_date],
            )

            prev_revenue_data = prev_revenue_entries.aggregate(
                total_credit=Coalesce(
                    Sum("reporting_credit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
                total_debit=Coalesce(
                    Sum("reporting_debit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )

            prev_total_revenue = (
                prev_revenue_data["total_credit"] - prev_revenue_data["total_debit"]
            )

            prev_employees = Employee.objects.filter(
                Q(joined_date__year__lte=prev_year)
                & (
                    Q(resignation_date__isnull=True)
                    | Q(resignation_date__year__gt=prev_year)
                )
            )

            prev_total_fte = 0
            prev_total_employees = 0
            prev_doctor_count = 0

            for emp in prev_employees:
                emp_type = getattr(emp, "category", None)
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                prev_total_fte += fte_value
                prev_total_employees += 1

                if emp.occupation == "Doctor":
                    prev_doctor_count += 1

            prev_revenue_per_fte = (
                float(prev_total_revenue) / prev_total_fte if prev_total_fte > 0 else 0
            )
            prev_revenue_per_doctor = (
                float(prev_total_revenue) / prev_doctor_count
                if prev_doctor_count > 0
                else 0
            )

            # Calculate previous year attrition rates
            prev_resigned_employees = Employee.objects.filter(
                resignation_date__year=prev_year, resignation_date__isnull=False
            )

            prev_voluntary_resignations = prev_resigned_employees.filter(
                resignation_reason__in=VOLUNTARY_REASONS
            ).count()

            prev_involuntary_resignations = prev_resigned_employees.filter(
                resignation_reason__in=INVOLUNTARY_REASONS
            ).count()

            prev_positive_attrition_rate = (
                (prev_voluntary_resignations / prev_total_employees * 100)
                if prev_total_employees > 0
                else 0
            )
            prev_negative_attrition_rate = (
                (prev_involuntary_resignations / prev_total_employees * 100)
                if prev_total_employees > 0
                else 0
            )

            # Calculate percentage changes
            revenue_per_fte_change = 0
            if prev_revenue_per_fte > 0:
                revenue_per_fte_change = (
                    (revenue_per_fte - prev_revenue_per_fte) / prev_revenue_per_fte
                ) * 100

            revenue_per_doctor_change = 0
            if prev_revenue_per_doctor > 0:
                revenue_per_doctor_change = (
                    (revenue_per_doctor - prev_revenue_per_doctor)
                    / prev_revenue_per_doctor
                ) * 100

            employees_change = 0
            if prev_total_employees > 0:
                employees_change = (
                    (total_employees - prev_total_employees) / prev_total_employees
                ) * 100

            # Calculate attrition rates based on resignation reasons
            # Positive attrition: voluntary departures
            # Negative attrition: involuntary departures

            resigned_employees = Employee.objects.filter(
                resignation_date__year=year, resignation_date__isnull=False
            )

            voluntary_resignations = resigned_employees.filter(
                resignation_reason__in=VOLUNTARY_REASONS
            ).count()

            involuntary_resignations = resigned_employees.filter(
                resignation_reason__in=INVOLUNTARY_REASONS
            ).count()

            positive_attrition_rate = (
                (voluntary_resignations / total_employees * 100)
                if total_employees > 0
                else 0
            )
            negative_attrition_rate = (
                (involuntary_resignations / total_employees * 100)
                if total_employees > 0
                else 0
            )

            # Calculate attrition rate percentage changes
            positive_attrition_change = 0
            if prev_positive_attrition_rate > 0:
                positive_attrition_change = (
                    (positive_attrition_rate - prev_positive_attrition_rate)
                    / prev_positive_attrition_rate
                ) * 100
            elif positive_attrition_rate > 0:
                positive_attrition_change = (
                    100  # New attrition where there was none before
                )

            negative_attrition_change = 0
            if prev_negative_attrition_rate > 0:
                negative_attrition_change = (
                    (negative_attrition_rate - prev_negative_attrition_rate)
                    / prev_negative_attrition_rate
                ) * 100
            elif negative_attrition_rate > 0:
                negative_attrition_change = (
                    100  # New attrition where there was none before
                )

            # Calculate historical data for charts (last 4 years)
            chart_years = list(range(year - 3, year + 1))
            historical_data = {}

            for chart_year in chart_years:
                # Revenue for this year
                chart_start_date = datetime(chart_year, 1, 1)
                chart_end_date = datetime(chart_year, 12, 31)

                chart_revenue_entries = JournalEntryTransaction.objects.filter(
                    chart_of_account__id__in=revenue_accounts,
                    transaction_date__range=[chart_start_date, chart_end_date],
                )

                chart_revenue_data = chart_revenue_entries.aggregate(
                    total_credit=Coalesce(
                        Sum("reporting_credit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                    total_debit=Coalesce(
                        Sum("reporting_debit_amount"),
                        Value(0),
                        output_field=DecimalField(),
                    ),
                )

                chart_total_revenue = (
                    chart_revenue_data["total_credit"]
                    - chart_revenue_data["total_debit"]
                )

                # Employees for this year
                chart_employees = Employee.objects.filter(
                    Q(joined_date__year__lte=chart_year)
                    & (
                        Q(resignation_date__isnull=True)
                        | Q(resignation_date__year__gt=chart_year)
                    )
                )

                chart_total_fte = 0
                chart_total_employees = 0
                chart_doctor_count = 0
                chart_resigned_employees = 0

                for emp in chart_employees:
                    emp_type = getattr(emp, "category", None)
                    fte_value = FTE_WEIGHTS.get(emp_type, 0.0)
                    chart_total_fte += fte_value
                    chart_total_employees += 1

                    if emp.occupation == "Doctor":
                        chart_doctor_count += 1

                # Count resignations for this year by category
                chart_resigned_employees = Employee.objects.filter(
                    resignation_date__year=chart_year, resignation_date__isnull=False
                )

                chart_voluntary_resignations = chart_resigned_employees.filter(
                    resignation_reason__in=VOLUNTARY_REASONS
                ).count()

                chart_involuntary_resignations = chart_resigned_employees.filter(
                    resignation_reason__in=INVOLUNTARY_REASONS
                ).count()

                # Calculate metrics for this year
                chart_revenue_per_fte = (
                    float(chart_total_revenue) / chart_total_fte
                    if chart_total_fte > 0
                    else 0
                )
                chart_revenue_per_doctor = (
                    float(chart_total_revenue) / chart_doctor_count
                    if chart_doctor_count > 0
                    else 0
                )
                chart_positive_attrition_rate = (
                    (chart_voluntary_resignations / chart_total_employees * 100)
                    if chart_total_employees > 0
                    else 0
                )
                chart_negative_attrition_rate = (
                    (chart_involuntary_resignations / chart_total_employees * 100)
                    if chart_total_employees > 0
                    else 0
                )

                historical_data[chart_year] = {
                    "revenue_per_fte": round(chart_revenue_per_fte, 0),
                    "revenue_per_doctor": round(chart_revenue_per_doctor, 0),
                    "employees": chart_total_employees,
                    "positive_attrition_rate": round(chart_positive_attrition_rate, 1),
                    "negative_attrition_rate": round(chart_negative_attrition_rate, 1),
                }

            overview_data = {
                "revenue_per_fte": {
                    "value": round(revenue_per_fte, 0),
                    "percentage": round(revenue_per_fte_change, 1),
                    "chart_data": [
                        {"name": str(y), "value": historical_data[y]["revenue_per_fte"]}
                        for y in chart_years
                    ],
                },
                "revenue_per_doctor": {
                    "value": round(revenue_per_doctor, 0),
                    "percentage": round(revenue_per_doctor_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["revenue_per_doctor"],
                        }
                        for y in chart_years
                    ],
                },
                "employees": {
                    "value": total_employees,
                    "percentage": round(employees_change, 1),
                    "chart_data": [
                        {"name": str(y), "value": historical_data[y]["employees"]}
                        for y in chart_years
                    ],
                },
                "positive_attrition_rate": {
                    "value": round(positive_attrition_rate, 1),
                    "percentage": round(positive_attrition_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["positive_attrition_rate"],
                        }
                        for y in chart_years
                    ],
                },
                "negative_attrition_rate": {
                    "value": round(negative_attrition_rate, 1),
                    "percentage": round(negative_attrition_change, 1),
                    "chart_data": [
                        {
                            "name": str(y),
                            "value": historical_data[y]["negative_attrition_rate"],
                        }
                        for y in chart_years
                    ],
                },
            }

            return custom_success_response(overview_data)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="doctors/chart")
    def doctors_chart(self, request):
        try:
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # Get clinic names and segments
            clinic_qs = Clinic.objects.exclude(name="Ceased Clinic / Codes").values(
                "name", "segment"
            )
            normalized_to_segment = {
                normalize_clinic_name(entry["name"]): entry["segment"]
                for entry in clinic_qs
            }

            # Get all relevant doctors
            employees = Employee.objects.filter(
                occupation="Doctor", joined_date__year__lte=end_year
            )

            # Prepare chart data
            chart_data = defaultdict(lambda: defaultdict(int))
            all_segments = set()

            for emp in employees:
                clinic_name = emp.clinic_name
                segment = match_clinic_segment(clinic_name, normalized_to_segment)
                all_segments.add(segment)

                for y in range(start_year, end_year + 1):
                    if emp.joined_date.year <= y and (
                        not emp.resignation_date or emp.resignation_date.year > y
                    ):
                        chart_data[y][segment] += 1
                        chart_data[y]["total"] += 1

            # Order segments with "Others" at the end
            final_segments = sorted([s for s in all_segments if s != "Others"])
            if "Others" in all_segments:
                final_segments.append("Others")

            # Build formatted result
            result = []
            for y in sorted(chart_data.keys()):
                year_entry = {"name": str(y)}
                for segment in final_segments:
                    year_entry[segment] = chart_data[y].get(segment, 0)
                year_entry["total"] = chart_data[y]["total"]
                result.append(year_entry)

            return custom_success_response(result)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="fte/chart")
    def fte_chart(self, request):
        try:
            year = int(request.query_params.get("year", datetime.now().year))
            last_n_years = int(request.query_params.get("last_n_years", 2))

            start_year = year - last_n_years
            end_year = year

            # FTE conversion weights
            FTE_WEIGHTS = {
                "Permanent Full Time": 1.0,
                "Permanent Part Time": 0.5,
                "Casual Workers": 0.3,
                "Intern": 0.2,
                "Visting Consultant": 0.4,
                "Contract Basis": 0.5,
            }

            # Department to Category mapping
            CATEGORY_MAP = {
                "Corporate": [
                    "HR",
                    "Finance",
                    "Information Technology",
                    "Marketing",
                    "Admin",
                    "Management",
                    "Operations",
                    "Sales",
                ],
                "PSA": ["Front Desk", "Call Centre", "Despatch"],
                "Clinic": [
                    "Clinical",
                    "Imaging",
                    "Optometry",
                    "Allied Health",
                    "Housekeeping",
                ],
                "Nurse": ["Nursing"],
                "Doctor": ["Doctor"],
            }

            def get_category(dept):
                for category, departments in CATEGORY_MAP.items():
                    if dept in departments:
                        return category
                return "Other"

            # Get all employees who joined before or during the end year
            employees = Employee.objects.filter(joined_date__year__lte=end_year)

            # Prepare chart data
            chart_data = defaultdict(lambda: defaultdict(float))

            for emp in employees:
                department = getattr(emp, "department", "").strip()
                category = get_category(department)
                emp_type = getattr(emp, "category", None)
                fte_value = FTE_WEIGHTS.get(emp_type, 0.0)

                joined_year = emp.joined_date.year if emp.joined_date else None
                resigned_year = (
                    emp.resignation_date.year if emp.resignation_date else None
                )

                for y in range(start_year, end_year + 1):
                    if (
                        joined_year
                        and joined_year <= y
                        and (not resigned_year or resigned_year > y)
                    ):
                        chart_data[y][category] += fte_value
                        chart_data[y]["total"] += fte_value

            # Format result for frontend
            result = []
            categories = list(CATEGORY_MAP.keys())

            for y in range(start_year, end_year + 1):
                year_entry = {"name": str(y)}
                for cat in categories:
                    year_entry[cat] = round(chart_data[y].get(cat, 0.0), 2)
                year_entry["total"] = round(chart_data[y].get("total", 0.0), 2)
                result.append(year_entry)

            return custom_success_response(result)

        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )

    # @method_decorator(cache_page(CACHE_TTL))
    @action(detail=False, methods=["get"], url_path="org-chart")
    def org_chart(self, request):
        try:
            employees = Employee.objects.filter(resignation_date__isnull=True)

            # Nested dict: clinic_name -> department_group -> occupation -> codes
            hierarchy = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))

            department_mapping = {
                "Doctor": "Doctor",
                "Clinical": "Clinical",
                "Front Desk": "Front Desk",
            }

            for emp in employees:
                clinic = emp.clinic_name
                department = emp.department
                occupation = emp.occupation
                code = emp.code

                # Only include mapped departments
                if department in department_mapping:
                    hierarchy[clinic][department_mapping[department]][
                        occupation
                    ].append(code)

            def build_tree(name, children_dict):
                children = []
                for key, val in children_dict.items():
                    if isinstance(val, dict):
                        children.append(build_tree(key, val))
                    elif isinstance(val, list):
                        children.append(
                            {"name": key, "children": [{"name": c} for c in val]}
                        )
                return {"name": name, "children": children}

            org_chart = {
                "name": "SMG",
                "children": [
                    build_tree(clinic, dept_data)
                    for clinic, dept_data in hierarchy.items()
                ],
            }

            return custom_success_response(org_chart)
        except Exception as e:
            traceback.print_exc()
            return custom_error_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST
            )


def normalize_clinic_name(name: str) -> str:
    if not name:
        return ""

    name = name.lower()
    name = re.sub(r"[^\w\s]", " ", name)
    name = re.sub(r"\b(at|@)\b", " at ", name)
    name = re.sub(r"\s+", " ", name).strip()

    replacements = {
        "togc": "the obstetrics gynaecology centre",
        "smg": "",
        "centre": "center",
        "centres": "centers",
        "womens": "women's",
        "kid's": "kids",
        "tai thong": "tai tong",
    }
    for old, new in replacements.items():
        name = name.replace(old, new)

    return name.strip()


def match_clinic_segment(name, normalized_to_segment):
    norm_name = normalize_clinic_name(name)
    match_result = process.extractOne(
        norm_name, normalized_to_segment.keys(), scorer=fuzz.token_sort_ratio
    )
    if match_result:
        match_name, score = match_result
        if score >= 70:
            return normalized_to_segment[match_name]
    return "Others"
